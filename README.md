Springboot Properties
=====================

This role will share the ssh (and other) credentials needed to ssh to a certain host.

Role Variables
--------------

    - ansible_ssh_user: "{{vault_ansible_ssh_user}}"
    - ansible_ssh_pass: "{{vault_ansible_ssh_pass}}"
    - ansible_become: "{{vault_ansible_become}}"
    - ansible_become_user: "{{vault_ansible_become_user}}"
    
    - key_store_password: "{{vault_key_store_password}}"
    - key_password: "{{vault_key_password}}"
  
Example playbook
----------------
    - hosts: {{host_env}}
      tasks:
      - include_role:
          name: ansible-variables

Author Information
------------------

<EMAIL>
