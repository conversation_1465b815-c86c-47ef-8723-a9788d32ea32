---
# handlers file for springboot
- name: springboot_restart
  systemd:
    name: "{{app_name}}.service"
    daemon_reload: true
    state: restarted

- name: springboot_stop
  systemd:
    name: "{{app_name}}.service"
    daemon_reload: true
    state: stopped

- name: springboot_start
  systemd:
    name: "{{app_name}}.service"
    daemon_reload: true
    state: started

  #shell: "{{app_path}}/control.sh restart"
  #become: yes
  #become_user: springboot
  
- name: springboot_unit_restart
  systemd:
    name: "{{app_name}}.service"
    daemon_reload: true
    state: restarted
